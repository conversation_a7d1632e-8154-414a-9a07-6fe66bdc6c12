import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../widgets/add_debt_bottom_sheet.dart';
import '../widgets/debt_card.dart';
import '../utils/number_formatter.dart';
import 'customer_debts_screen.dart';
import 'customer_payments_screen.dart';
import 'customer_info_screen.dart';
import 'customer_daily_debts_screen.dart';
import 'account_statement_screen.dart';
import 'customer_limit_screen.dart';
import 'overdue_debts_screen.dart';

// أنواع عرض البطاقات في صفحة تفاصيل العميل
enum CustomerDetailViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  grid, // عرض شبكي
}

class CustomerDetailScreen extends StatefulWidget {
  const CustomerDetailScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with TickerProviderStateMixin {
  // نوع العرض الحالي
  CustomerDetailViewType _currentViewType = CustomerDetailViewType.standard;

  // TabController للإحصائيات
  TabController? _statisticsTabController;

  @override
  void initState() {
    super.initState();
    // تهيئة TabController للإحصائيات
    _statisticsTabController = TabController(length: 2, vsync: this);
    // تحميل نوع العرض المحفوظ
    _loadSavedViewType();
    // Load customer debts and payments
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      await debtProvider.loadCustomerDebts(widget.customer.id!);
      await debtProvider.loadCustomerPayments(widget.customer.id!);
      // إجبار إعادة بناء الواجهة
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _statisticsTabController?.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts and payments when returning to this screen
    _refreshData();
  }

  Future<void> _refreshData() async {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    await debtProvider.loadCustomerDebts(widget.customer.id!);
    await debtProvider.loadCustomerPayments(widget.customer.id!);
    // إجبار إعادة بناء الواجهة
    if (mounted) {
      setState(() {});
    }
  }

  // التنقل إلى ديون العميل اليوم
  void _navigateToCustomerTodayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: widget.customer,
          isToday: true,
          title: 'ديون اليوم',
        ),
      ),
    );
  }

  // التنقل إلى ديون العميل الأمس
  void _navigateToCustomerYesterdayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: widget.customer,
          isToday: false,
          title: 'ديون الأمس',
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer.name),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر تغيير نوع العرض
          PopupMenuButton<CustomerDetailViewType>(
            icon: Icon(_getViewTypeIcon(_currentViewType), color: Colors.white),
            tooltip: 'تغيير نوع العرض',
            onSelected: (CustomerDetailViewType viewType) {
              setState(() {
                _currentViewType = viewType;
              });
              _saveViewType(viewType);
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: CustomerDetailViewType.standard,
                child: _buildViewTypeMenuItem(
                  CustomerDetailViewType.standard,
                  Icons.view_agenda,
                  'عرض عادي',
                  'البطاقات العادية',
                ),
              ),
              PopupMenuItem(
                value: CustomerDetailViewType.compact,
                child: _buildViewTypeMenuItem(
                  CustomerDetailViewType.compact,
                  Icons.view_list,
                  'عرض مضغوط',
                  'بطاقات صغيرة',
                ),
              ),
              PopupMenuItem(
                value: CustomerDetailViewType.grid,
                child: _buildViewTypeMenuItem(
                  CustomerDetailViewType.grid,
                  Icons.grid_view,
                  'عرض شبكي',
                  'بطاقات في شبكة',
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Customer Info Card
            Container(
              margin: const EdgeInsets.all(20),
              child: _buildSimpleCustomerInfoCard(),
            ),

            // Navigation Buttons
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCardsView(),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة إضافة دين جديد
  Widget _buildAddDebtCard() {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade700],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            await showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) =>
                  AddDebtBottomSheet(customer: widget.customer),
            );

            // تحديث البيانات عند العودة
            if (mounted) {
              await _refreshData();
            }
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Icon(
                    Icons.add_circle,
                    size: 28,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(width: 16),

                // Content Section
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'إضافة دين جديد',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'إضافة كارت جديد لهذا العميل',
                        style: TextStyle(fontSize: 12, color: Colors.white70),
                      ),
                    ],
                  ),
                ),

                // Arrow Section
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة معلومات العميل المبسطة
  Widget _buildSimpleCustomerInfoCard() {
    return FutureBuilder<List<Debt>>(
      key: ValueKey(
        'customer_info_${widget.customer.id}_${DateTime.now().millisecondsSinceEpoch}',
      ),
      future: Future.value(
        Provider.of<DebtProvider>(
          context,
          listen: false,
        ).debts.where((debt) => debt.customerId == widget.customer.id).toList(),
      ),
      builder: (context, snapshot) {
        final customerDebts = snapshot.data ?? [];

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      CustomerInfoScreen(customer: widget.customer),
                ),
              );
            },
            borderRadius: BorderRadius.circular(20),
            child: Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Icon(
                    Icons.person,
                    size: 32,
                    color: Colors.grey.shade600,
                  ),
                ),

                const SizedBox(width: 16),

                // Content Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'معلومات العميل',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        widget.customer.name,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.black54,
                        ),
                      ),
                      if (widget.customer.phone != null &&
                          widget.customer.phone!.isNotEmpty) ...[
                        const SizedBox(height: 1),
                        Text(
                          widget.customer.phone!,
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Arrow and Stats
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${customerDebts.fold(0, (sum, debt) => sum + debt.quantity)}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'عرض التفاصيل',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة إحصائيات متقدمة
  Widget _buildAdvancedStatisticsCard({required VoidCallback onPressed}) {
    return FutureBuilder<List<Debt>>(
      key: ValueKey(
        'advanced_stats_${widget.customer.id}_${DateTime.now().millisecondsSinceEpoch}',
      ),
      future: Future.value(
        Provider.of<DebtProvider>(
          context,
          listen: false,
        ).debts.where((debt) => debt.customerId == widget.customer.id).toList(),
      ),
      builder: (context, snapshot) {
        final customerDebts = snapshot.data ?? [];

        final totalAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final overdueDebts =
            customerDebts.where((debt) => debt.isOverdue).length;

        return Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon Section
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        size: 32,
                        color: Colors.grey.shade600,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Content Section
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'إحصائيات الديون',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'إجمالي: ${NumberFormatter.formatCurrency(totalAmount.toInt())}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Stats Section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                overdueDebts > 0
                                    ? Icons.warning
                                    : Icons.check_circle,
                                size: 12,
                                color: overdueDebts > 0
                                    ? Colors.red
                                    : Colors.green,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                overdueDebts > 0
                                    ? '$overdueDebts متأخر'
                                    : 'منتظم',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: overdueDebts > 0
                                      ? Colors.red
                                      : Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${customerDebts.fold(0, (sum, debt) => sum + debt.quantity)} كارت',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة عمل متقدمة مع معلومات احترافية
  Widget _buildAdvancedActionCard({
    required VoidCallback onPressed,
    required IconData icon,
    required String title,
    required Color color,
    required Gradient gradient,
    required bool isDebtCard,
    bool isFullWidth = false,
  }) {
    return FutureBuilder<List<Debt>>(
      key: ValueKey(
        'action_card_${widget.customer.id}_$title${DateTime.now().millisecondsSinceEpoch}',
      ),
      future: Future.value(
        Provider.of<DebtProvider>(
          context,
          listen: false,
        ).debts.where((debt) => debt.customerId == widget.customer.id).toList(),
      ),
      builder: (context, snapshot) {
        // حساب الإحصائيات للعميل
        final customerDebts = snapshot.data ?? [];

        final totalAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final paidAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.paidAmount,
        );
        final remainingAmount = totalAmount - paidAmount;

        final pendingDebts = customerDebts
            .where((debt) => debt.status == DebtStatus.pending)
            .length;
        final paidDebts = customerDebts
            .where((debt) => debt.status == DebtStatus.paid)
            .length;
        final overdueDebts =
            customerDebts.where((debt) => debt.isOverdue).length;

        // معلومات مختلفة حسب نوع البطاقة
        String mainValue;
        String subtitle;
        String statusText;
        IconData statusIcon;
        Color statusColor;

        if (isDebtCard) {
          mainValue =
              '${customerDebts.fold(0, (sum, debt) => sum + debt.quantity)}';
          subtitle = '${remainingAmount.toStringAsFixed(0)} د.ع متبقي';
          if (overdueDebts > 0) {
            statusText = '$overdueDebts متأخر';
            statusIcon = Icons.warning;
            statusColor = Colors.red;
          } else if (pendingDebts > 0) {
            statusText = '$pendingDebts معلق';
            statusIcon = Icons.schedule;
            statusColor = Colors.orange;
          } else {
            statusText = 'مسدد بالكامل';
            statusIcon = Icons.check_circle;
            statusColor = Colors.green;
          }
        } else {
          // بطاقات أخرى (تسديدات، كشف حساب، إدارة سقف)
          if (title == 'قائمة التسديدات') {
            mainValue = '$paidDebts';
            subtitle = '${paidAmount.toStringAsFixed(0)} د.ع مسدد';
            final paymentRate = customerDebts.isNotEmpty
                ? (paidDebts / customerDebts.length * 100).toStringAsFixed(0)
                : '0';
            statusText = '$paymentRate% معدل السداد';
            statusIcon = Icons.trending_up;
            statusColor = Colors.blue;
          } else if (title == 'كشف الحساب') {
            mainValue =
                '${customerDebts.fold(0, (sum, debt) => sum + debt.quantity)}';
            subtitle = 'عملية مالية';
            statusText = 'تفصيلي';
            statusIcon = Icons.receipt_long;
            statusColor = Colors.blue;
          } else if (title == 'إدارة السقف') {
            final hasLimit = widget.customer.creditLimit != null &&
                widget.customer.creditLimit! > 0;
            mainValue = hasLimit
                ? widget.customer.creditLimit!.toStringAsFixed(0)
                : '∞';
            subtitle = hasLimit ? 'سقف محدد' : 'بدون حدود';
            statusText = hasLimit ? 'مفعل' : 'غير مفعل';
            statusIcon = hasLimit ? Icons.check_circle : Icons.warning;
            statusColor = hasLimit ? Colors.green : Colors.orange;
          } else {
            // افتراضي
            mainValue = '$paidDebts';
            subtitle = '${paidAmount.toStringAsFixed(0)} د.ع مسدد';
            statusText = 'نشط';
            statusIcon = Icons.trending_up;
            statusColor = Colors.blue;
          }
        }

        return Container(
          width: isFullWidth ? double.infinity : null,
          height: isFullWidth ? 100 : 120,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: isFullWidth
                    ? Row(
                        children: [
                          // Icon Section
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Icon(
                              icon,
                              size: 32,
                              color: Colors.grey.shade600,
                            ),
                          ),

                          const SizedBox(width: 16),

                          // Content Section
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  title,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  subtitle,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Stats Section
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                mainValue,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      statusIcon,
                                      size: 12,
                                      color: statusColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      statusText,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: statusColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Row
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: Icon(
                                  icon,
                                  size: 24,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      statusIcon,
                                      size: 12,
                                      color: statusColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      statusText,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: statusColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          // Main Value
                          Text(
                            mainValue,
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 4),

                          // Title and Subtitle
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            subtitle,
                            style: const TextStyle(
                              fontSize: 11,
                              color: Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة عمل مدمجة بحجم صغير
  Widget _buildCompactActionCard({
    required VoidCallback onPressed,
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: color.withValues(alpha: 0.3)),
                  ),
                  child: Icon(icon, size: 20, color: color),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey.shade400,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showStatisticsBottomSheet(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _StatisticsTabScreen(
          customer: widget.customer,
        ),
      ),
    );
  }

  Widget _buildStatisticsContent(BuildContext context) {
    return Consumer<CardTypeProvider>(
      builder: (context, cardTypeProvider, child) {
        return FutureBuilder<List<Debt>>(
          key: ValueKey(
            'statistics_content_${widget.customer.id}_${DateTime.now().millisecondsSinceEpoch}',
          ),
          future: Future.value(
            Provider.of<DebtProvider>(context, listen: false)
                .debts
                .where((debt) => debt.customerId == widget.customer.id)
                .toList(),
          ),
          builder: (context, snapshot) {
            final customerDebts = snapshot.data ?? [];

            if (customerDebts.isEmpty) {
              return const Center(
                child: Text(
                  'لا توجد ديون لهذا العميل',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              );
            }

            final now = DateTime.now();
            final today = DateTime(now.year, now.month, now.day);
            final yesterday = today.subtract(const Duration(days: 1));
            final weekStart = today.subtract(Duration(days: today.weekday - 1));
            final monthStart = DateTime(now.year, now.month);
            final last30Days = today.subtract(const Duration(days: 30));

            // حسابات اليوم (المبيعات فقط - ديون العملاء لك)
            final todayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(today) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final todaySales = todayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final todayCount =
                todayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الأمس (المبيعات فقط - ديون العملاء لك)
            final yesterdayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(yesterday) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final yesterdaySales = yesterdayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final yesterdayCount =
                yesterdayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الأسبوع (المبيعات فقط - ديون العملاء لك)
            final weekDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(weekStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final weekSales = weekDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final weekCount =
                weekDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الشهر (المبيعات فقط - ديون العملاء لك)
            final monthDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(monthStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final monthSales = monthDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final monthCount =
                monthDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // نشاط العميل (آخر 30 يوم)
            final recentDebts = customerDebts
                .where((debt) => debt.entryDate.isAfter(last30Days))
                .toList();
            final activityPercentage = customerDebts.isNotEmpty
                ? (recentDebts.length / customerDebts.length * 100)
                : 0.0;

            // إحصائيات أنواع الكروت مع المبالغ والكمية الصحيحة
            final cardTypeStats = <String, Map<String, dynamic>>{};
            for (final debt in customerDebts) {
              if (!cardTypeStats.containsKey(debt.cardType)) {
                cardTypeStats[debt.cardType] = {
                  'count': 0,
                  'amount': 0.0,
                  'quantity': 0,
                };
              }
              cardTypeStats[debt.cardType]!['count'] =
                  (cardTypeStats[debt.cardType]!['count'] as int) + 1;
              cardTypeStats[debt.cardType]!['amount'] =
                  (cardTypeStats[debt.cardType]!['amount'] as double) +
                      debt.amount;
              cardTypeStats[debt.cardType]!['quantity'] =
                  (cardTypeStats[debt.cardType]!['quantity'] as int) +
                      debt.quantity;
            }
            final sortedCardTypes = cardTypeStats.entries.toList()
              ..sort(
                (a, b) => (b.value['count'] as int).compareTo(
                  a.value['count'] as int,
                ),
              );

            // الديون المتأخرة
            final overdueDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isBefore(today);
            }).toList();
            final overdueAmount = overdueDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            // الديون المستحقة اليوم
            final dueTodayDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isAtSameMomentAs(today);
            }).toList();
            final dueTodayAmount = dueTodayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم أنواع الكروت
                  if (sortedCardTypes.isNotEmpty) ...[
                    const Text(
                      'أنواع الكروت',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // بطاقة إجمالي الكارتات الشاملة
                    _buildComprehensiveCardSummary(
                      sortedCardTypes,
                      cardTypeProvider,
                    ),

                    const SizedBox(height: 16),

                    // عرض أنواع الكروت في عمود
                    Column(
                      children: sortedCardTypes.map((cardTypeEntry) {
                        final cardTypeId = cardTypeEntry.key;
                        final cardTypeData = cardTypeEntry.value;

                        final amount = cardTypeData['amount'] as double;
                        final quantity = cardTypeData['quantity'] as int;

                        // تحويل نوع الكارت إلى اسم عربي أولاً
                        final String cardTypeName = _getCardTypeDisplayName(
                          cardTypeId,
                          cardTypeProvider,
                        );

                        // تحديد اللون بناءً على نوع الكارت
                        MaterialColor color;
                        if (cardTypeName == 'زين') {
                          color = Colors.purple; // بنفسجي فاتح لزين
                        } else if (cardTypeName == 'أبو العشرة') {
                          color = Colors.cyan; // فايروسي فاتح لأبو العشرة
                        } else if (cardTypeName == 'آسيا') {
                          color = Colors.red;
                        } else if (cardTypeName == 'أبو الستة') {
                          color = Colors.grey;
                        } else {
                          // للأنواع الأخرى، استخدم الألوان الافتراضية
                          final colors = [
                            Colors.orange,
                            Colors.blue,
                            Colors.green,
                            Colors.teal,
                            Colors.indigo,
                          ];
                          final colorIndex =
                              sortedCardTypes.indexOf(cardTypeEntry);
                          color = colors[colorIndex % colors.length];
                        }

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: _buildCardTypeStatCard(
                            cardTypeName,
                            quantity,
                            amount,
                            color,
                            customerDebts.length,
                            customerDebts.fold(
                                0, (sum, debt) => sum + debt.quantity),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 20),
                  ],

                  // الصف الأول - مبيعات اليوم والأمس
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _navigateToCustomerTodayDebts(context),
                          child: _buildStatCard(
                            'مبيعات اليوم',
                            _formatNumber(todaySales),
                            Icons.today,
                            Colors.blue,
                            'د.ع',
                            subtitle: '$todayCount كارت',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: GestureDetector(
                          onTap: () =>
                              _navigateToCustomerYesterdayDebts(context),
                          child: _buildStatCard(
                            'مبيعات الأمس',
                            _formatNumber(yesterdaySales),
                            Icons.history,
                            Colors.teal,
                            'د.ع',
                            subtitle: '$yesterdayCount كارت',
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // الصف الثاني - مبيعات الأسبوع والشهر
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الأسبوع',
                          _formatNumber(weekSales),
                          Icons.date_range,
                          Colors.green,
                          'د.ع',
                          subtitle: '$weekCount كارت',
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الشهر',
                          _formatNumber(monthSales),
                          Icons.calendar_month,
                          Colors.purple,
                          'د.ع',
                          subtitle: '$monthCount كارت',
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // بطاقة نشاط العميل - عريضة
                  _buildStatCard(
                    'نشاط العميل',
                    activityPercentage.toStringAsFixed(1),
                    Icons.trending_up,
                    Colors.cyan,
                    '%',
                    subtitle: 'آخر 30 يوم',
                  ),

                  const SizedBox(height: 12),

                  // الصف الرابع - الديون المتأخرة والمستحقة اليوم
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => OverdueDebtsScreen(
                                  customer: widget.customer,
                                  overdueDebts: overdueDebts,
                                ),
                              ),
                            );
                          },
                          child: _buildStatCard(
                            'ديون متأخرة',
                            _formatNumber(overdueAmount),
                            Icons.warning,
                            Colors.red,
                            'د.ع',
                            subtitle:
                                '${overdueDebts.fold(0, (sum, debt) => sum + debt.quantity)} كارت',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'استحقاق اليوم',
                          _formatNumber(dueTodayAmount),
                          Icons.schedule,
                          Colors.amber,
                          'د.ع',
                          subtitle:
                              '${dueTodayDebts.fold(0, (sum, debt) => sum + debt.quantity)} كارت',
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, color.shade50.withValues(alpha: 0.3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7),
            blurRadius: 8,
            offset: const Offset(-3, -3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title Row
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [color.shade400, color.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Value
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color.shade700,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 12,
                  color: color.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 11,
                  color: color.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // دالة تنسيق الأرقام مع فاصل الآلاف
  String _formatNumber(double number) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(number.round());
  }

  // دالة تحويل نوع الكارت إلى اسم عربي
  String _getCardTypeDisplayName(
    String cardTypeId,
    CardTypeProvider cardTypeProvider,
  ) {
    // إزالة كلمة "متعدد:" إذا كانت موجودة
    String cleanCardTypeId = cardTypeId;
    if (cardTypeId.startsWith('متعدد: ')) {
      cleanCardTypeId = cardTypeId.substring(7); // إزالة "متعدد: "

      // إذا كان النص يحتوي على عدة أنواع مفصولة بفاصلة، عرض جميع الأنواع
      if (cleanCardTypeId.contains(', ')) {
        final cardTypes = cleanCardTypeId.split(', ');
        final displayNames = cardTypes.map((cardType) {
          // إزالة الأقواس والأرقام إذا كانت موجودة
          final cleanType =
              cardType.replaceAll(RegExp(r'\s*\(\d+\)'), '').trim();
          return _getSingleCardTypeDisplayName(cleanType, cardTypeProvider);
        }).toList();
        return displayNames.join(' + '); // دمج الأسماء بعلامة +
      }
    }

    return _getSingleCardTypeDisplayName(cleanCardTypeId, cardTypeProvider);
  }

  // دالة مساعدة للحصول على اسم نوع كارت واحد
  String _getSingleCardTypeDisplayName(
    String cardTypeId,
    CardTypeProvider cardTypeProvider,
  ) {
    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardTypeId,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardTypeId,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardTypeId;
      }
    }
  }

  // دالة للحصول على لون نوع الكارت
  Color _getCardTypeColor(String cardTypeId) {
    // إزالة كلمة "متعدد:" إذا كانت موجودة
    String cleanCardTypeId = cardTypeId;
    if (cardTypeId.startsWith('متعدد: ')) {
      cleanCardTypeId = cardTypeId.substring(7);
      // إذا كان النص يحتوي على عدة أنواع، خذ الأول
      if (cleanCardTypeId.contains(', ')) {
        cleanCardTypeId = cleanCardTypeId
            .split(', ')
            .first
            .replaceAll(RegExp(r'\s*\(\d+\)'), '')
            .trim();
      }
    }

    // ألوان الكارتات العراقية
    switch (cleanCardTypeId.toLowerCase().trim()) {
      case 'زين':
      case 'zain':
        return Colors.purple;
      case 'آسيا':
      case 'اسيا':
      case 'asia':
      case 'sia':
        return Colors.red;
      case 'أبو الستة':
      case 'ابو الستة':
      case 'abusitta':
        return Colors.grey;
      case 'أبو العشرة':
      case 'ابو العشرة':
      case 'abuashara':
        return Colors.cyan;
      case 'cash':
      case 'نقدي':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  // بناء بطاقة إجمالي الكارتات الشاملة
  Widget _buildComprehensiveCardSummary(
    List<MapEntry<String, Map<String, dynamic>>> sortedCardTypes,
    CardTypeProvider cardTypeProvider,
  ) {
    // حساب الإجماليات
    final totalQuantity = sortedCardTypes.fold<int>(
      0,
      (sum, entry) => sum + ((entry.value['quantity'] as int?) ?? 0),
    );

    final totalAmount = sortedCardTypes.fold<double>(
      0,
      (sum, entry) => sum + ((entry.value['amount'] as double?) ?? 0),
    );

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان الرئيسي
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade500, Colors.blue.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.assessment,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'ملخص شامل للكارتات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1565C0),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // الإحصائيات الرئيسية في شبكة
          Column(
            children: [
              // البطاقة الرئيسية: إجمالي المبلغ
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: Colors.grey.shade700,
                        size: 24,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        NumberFormatter.formatCurrency(totalAmount.toInt()),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'إجمالي قيمة الكارتات',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 10),

              // الصف الثاني: إجمالي الكارتات وعدد الأنواع
              IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // إجمالي الكارتات
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.shade200),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.inventory_2,
                              color: Colors.blue.shade600,
                              size: 22,
                            ),
                            const SizedBox(height: 6),
                            Text(
                              '$totalQuantity',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'إجمالي الكارتات',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 10),

                    // عدد الأنواع
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.orange.shade200),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.category,
                              color: Colors.orange.shade600,
                              size: 22,
                            ),
                            const SizedBox(height: 6),
                            Text(
                              '${sortedCardTypes.length}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'أنواع مختلفة',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // تفاصيل الأنواع - تصميم أنيق ومحسن
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.grey.shade50],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.grey.shade200),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان القسم مع تصميم أنيق
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.purple.shade400, Colors.purple.shade600],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.analytics, color: Colors.white, size: 22),
                      SizedBox(width: 10),
                      Text(
                        'توزيع الأنواع',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // قائمة الأنواع مع تصميم محسن
                ...sortedCardTypes.take(3).map((entry) {
                  final cardTypeId = entry.key;
                  final cardTypeData = entry.value;
                  final quantity = cardTypeData['quantity'] as int;
                  final amount = cardTypeData['amount'] as double;
                  final percentage = quantity / totalQuantity * 100;

                  final cardTypeName = _getCardTypeDisplayName(
                    cardTypeId,
                    cardTypeProvider,
                  );
                  final color = _getCardTypeColor(cardTypeId);

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(color: color.withValues(alpha: 0.2)),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.1),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // الصف الأول: الأيقونة والاسم والنسبة
                        Row(
                          children: [
                            // أيقونة ملونة
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [color.withValues(alpha: 0.8), color],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: color.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.credit_card,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),

                            const SizedBox(width: 10),

                            // اسم النوع
                            Expanded(
                              child: Text(
                                cardTypeName,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ),

                            // النسبة المئوية
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    color.withValues(alpha: 0.1),
                                    color.withValues(alpha: 0.2),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: color.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                '${percentage.toStringAsFixed(1)}%',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: color,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 10),

                        // الصف الثاني: شريط التقدم والتفاصيل
                        Column(
                          children: [
                            // شريط التقدم الأنيق
                            Container(
                              height: 6,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: percentage / 100,
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        color.withValues(alpha: 0.7),
                                        color,
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: color.withValues(alpha: 0.4),
                                        blurRadius: 2,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 6),

                            // تفاصيل الكمية والمبلغ
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // الكمية
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: color.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: color.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.inventory_2,
                                        color: color,
                                        size: 14,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$quantity كارت',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: color,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // المبلغ
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color:
                                          Colors.green.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.attach_money,
                                        color: Colors.green.shade600,
                                        size: 14,
                                      ),
                                      const SizedBox(width: 2),
                                      Text(
                                        NumberFormatter.formatCurrency(
                                          amount.toInt(),
                                        ),
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                }),

                // إظهار المزيد إذا كان هناك أكثر من 3 أنواع
                if (sortedCardTypes.length > 3)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.more_horiz,
                          color: Colors.grey.shade600,
                          size: 18,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'و ${sortedCardTypes.length - 3} أنواع أخرى',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائيات نوع الكارت
  Widget _buildCardTypeStatCard(
    String cardTypeName,
    int quantity,
    double amount,
    MaterialColor color,
    int totalDebts,
    int totalQuantity,
  ) {
    final percentage = totalDebts > 0 ? (quantity / totalQuantity * 100) : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7),
            blurRadius: 8,
            offset: const Offset(-3, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة نوع الكارت
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.shade400, color.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Icon(Icons.credit_card, color: Colors.white, size: 24),
          ),

          const SizedBox(width: 16),

          // معلومات نوع الكارت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم نوع الكارت
                Text(
                  cardTypeName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                const SizedBox(height: 8),

                // صف العدد والمبلغ
                Row(
                  children: [
                    // عدد الكروت
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'العدد',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Text(
                                '$quantity',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                quantity == 1 ? 'كارت' : 'كارت',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // المبلغ
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'المبلغ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  NumberFormatter.formatCurrency(
                                      amount.toInt()),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // النسبة المئوية
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${percentage.toStringAsFixed(1)}% من إجمالي الديون',
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.black54,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء عرض البطاقات حسب النوع المحدد
  Widget _buildCardsView() {
    switch (_currentViewType) {
      case CustomerDetailViewType.standard:
        return _buildStandardCardsView();
      case CustomerDetailViewType.compact:
        return _buildCompactCardsView();
      case CustomerDetailViewType.grid:
        return _buildGridCardsView();
    }
  }

  // العرض العادي (الحالي)
  Widget _buildStandardCardsView() {
    return Column(
      children: [
        // Debts Card (Full Width)
        _buildAdvancedActionCard(
          onPressed: () async {
            final debtProvider = Provider.of<DebtProvider>(
              context,
              listen: false,
            );
            await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerDebtsScreen(customer: widget.customer),
              ),
            );
            // Refresh payments when returning from debts screen
            if (mounted) {
              await debtProvider.loadCustomerPayments(widget.customer.id!);
            }
          },
          icon: Icons.receipt_long,
          title: 'قائمة الديون',
          color: Colors.orange,
          gradient: LinearGradient(
            colors: [Colors.grey.shade200, Colors.grey.shade400],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          isDebtCard: true,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // Payments Card (Full Width)
        _buildAdvancedActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerPaymentsScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.payment,
          title: 'قائمة التسديدات',
          color: Colors.green,
          gradient: LinearGradient(
            colors: [Colors.grey.shade300, Colors.grey.shade500],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          isDebtCard: false,
          isFullWidth: true,
        ),

        const SizedBox(height: 16),

        // Statistics Card (Full Width)
        _buildAdvancedStatisticsCard(
          onPressed: () {
            _showStatisticsBottomSheet(context);
          },
        ),

        const SizedBox(height: 16),

        // Account Statement and Credit Limit Row
        Row(
          children: [
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          AccountStatementScreen(customer: widget.customer),
                    ),
                  );
                },
                icon: Icons.receipt_long_outlined,
                title: 'كشف الحساب',
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CustomerLimitScreen(customer: widget.customer),
                    ),
                  );
                },
                icon: Icons.account_balance_wallet_outlined,
                title: 'إدارة السقف',
                color: Colors.indigo,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Add Debt Card (Full Width)
        _buildAddDebtCard(),
      ],
    );
  }

  // العرض المضغوط
  Widget _buildCompactCardsView() {
    return Column(
      children: [
        // صف الديون والتسديدات
        Row(
          children: [
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () async {
                  final debtProvider = Provider.of<DebtProvider>(
                    context,
                    listen: false,
                  );
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CustomerDebtsScreen(customer: widget.customer),
                    ),
                  );
                  if (mounted) {
                    await debtProvider.loadCustomerPayments(
                      widget.customer.id!,
                    );
                  }
                },
                icon: Icons.receipt_long,
                title: 'قائمة الديون',
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CustomerPaymentsScreen(customer: widget.customer),
                    ),
                  );
                },
                icon: Icons.payment,
                title: 'قائمة التسديدات',
                color: Colors.green,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // صف الإحصائيات وكشف الحساب
        Row(
          children: [
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  _showStatisticsBottomSheet(context);
                },
                icon: Icons.analytics,
                title: 'الإحصائيات',
                color: Colors.purple,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          AccountStatementScreen(customer: widget.customer),
                    ),
                  );
                },
                icon: Icons.receipt_long_outlined,
                title: 'كشف الحساب',
                color: Colors.blue,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // صف إدارة السقف وإضافة دين
        Row(
          children: [
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CustomerLimitScreen(customer: widget.customer),
                    ),
                  );
                },
                icon: Icons.account_balance_wallet_outlined,
                title: 'إدارة السقف',
                color: Colors.indigo,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCompactActionCard(
                onPressed: () async {
                  await showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (context) =>
                        AddDebtBottomSheet(customer: widget.customer),
                  );

                  if (mounted) {
                    final debtProvider = Provider.of<DebtProvider>(
                      context,
                      listen: false,
                    );
                    debtProvider.loadCustomerDebts(widget.customer.id!);
                    debtProvider.loadCustomerPayments(widget.customer.id!);
                  }
                },
                icon: Icons.add_circle,
                title: 'إضافة دين',
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // العرض الشبكي
  Widget _buildGridCardsView() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildGridActionCard(
          onPressed: () async {
            final debtProvider = Provider.of<DebtProvider>(
              context,
              listen: false,
            );
            await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerDebtsScreen(customer: widget.customer),
              ),
            );
            if (mounted) {
              await debtProvider.loadCustomerPayments(widget.customer.id!);
            }
          },
          icon: Icons.receipt_long,
          title: 'قائمة الديون',
          color: Colors.orange,
        ),
        _buildGridActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerPaymentsScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.payment,
          title: 'قائمة التسديدات',
          color: Colors.green,
        ),
        _buildGridActionCard(
          onPressed: () {
            _showStatisticsBottomSheet(context);
          },
          icon: Icons.analytics,
          title: 'الإحصائيات',
          color: Colors.purple,
        ),
        _buildGridActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    AccountStatementScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.receipt_long_outlined,
          title: 'كشف الحساب',
          color: Colors.blue,
        ),
        _buildGridActionCard(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerLimitScreen(customer: widget.customer),
              ),
            );
          },
          icon: Icons.account_balance_wallet_outlined,
          title: 'إدارة السقف',
          color: Colors.indigo,
        ),
        _buildGridActionCard(
          onPressed: () async {
            await showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) =>
                  AddDebtBottomSheet(customer: widget.customer),
            );

            if (mounted) {
              await _refreshData();
            }
          },
          icon: Icons.add_circle,
          title: 'إضافة دين',
          color: Colors.blue,
        ),
      ],
    );
  }

  // دالة لبناء بطاقة شبكية
  Widget _buildGridActionCard({
    required VoidCallback onPressed,
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دالة لتحميل نوع العرض المحفوظ
  Future<void> _loadSavedViewType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewType = prefs.getString('customer_detail_view_type');

      if (savedViewType != null) {
        final viewType = CustomerDetailViewType.values.firstWhere(
          (e) => e.name == savedViewType,
          orElse: () => CustomerDetailViewType.standard,
        );

        setState(() {
          _currentViewType = viewType;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع العرض: $e');
    }
  }

  // دالة للحصول على أيقونة نوع العرض
  IconData _getViewTypeIcon(CustomerDetailViewType viewType) {
    switch (viewType) {
      case CustomerDetailViewType.standard:
        return Icons.view_agenda;
      case CustomerDetailViewType.compact:
        return Icons.view_list;
      case CustomerDetailViewType.grid:
        return Icons.grid_view;
    }
  }

  // دالة لبناء عنصر قائمة نوع العرض
  Widget _buildViewTypeMenuItem(
    CustomerDetailViewType viewType,
    IconData icon,
    String title,
    String description,
  ) {
    final isSelected = _currentViewType == viewType;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: Colors.blue.withValues(alpha: 0.3))
            : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: isSelected ? Colors.blue[700] : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                    color: isSelected ? Colors.blue[700] : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.blue[600] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(Icons.check_circle, color: Colors.blue[700], size: 18),
        ],
      ),
    );
  }

  // دالة لحفظ نوع العرض المختار
  Future<void> _saveViewType(CustomerDetailViewType viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('customer_detail_view_type', viewType.name);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع العرض: $e');
    }
  }

  // حساب إحصائيات السداد
  Future<Map<String, dynamic>> _calculatePaymentStatistics(
      DebtProvider debtProvider) async {
    final customerDebts = debtProvider.debts
        .where((debt) => debt.customerId == widget.customer.id)
        .toList();

    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (customerDebts.isEmpty) {
      return {
        'totalPaidAmount': 0.0,
        'totalPaidCount': 0,
        'paymentRate': 0.0,
        'todayPayments': {'count': 0, 'amount': 0.0},
        'yesterdayPayments': {'count': 0, 'amount': 0.0},
        'weeklyPayments': {'count': 0, 'amount': 0.0},
        'monthlyPayments': {'count': 0, 'amount': 0.0},
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final monthStart = DateTime(now.year, now.month);

    // إجمالي المدفوعات
    final totalPaidAmount =
        customerDebts.fold(0.0, (sum, debt) => sum + debt.paidAmount);
    final totalPaidCount =
        customerDebts.where((debt) => debt.paidAmount > 0).length;

    // معدل السداد
    final totalAmount =
        customerDebts.fold(0.0, (sum, debt) => sum + debt.amount);
    final paymentRate =
        totalAmount > 0 ? (totalPaidAmount / totalAmount * 100) : 0.0;

    // سداد اليوم
    final todayPayments = customerPayments.where((payment) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );
      return paymentDate.isAtSameMomentAs(today);
    }).toList();

    final todayPaymentAmount =
        todayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الأمس
    final yesterdayPayments = customerPayments.where((payment) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );
      return paymentDate.isAtSameMomentAs(yesterday);
    }).toList();

    final yesterdayPaymentAmount =
        yesterdayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الأسبوع
    final weeklyPayments = customerPayments
        .where((payment) => payment.paymentDate.isAfter(weekStart))
        .toList();
    final weeklyPaymentAmount =
        weeklyPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الشهر
    final monthlyPayments = customerPayments
        .where((payment) => payment.paymentDate.isAfter(monthStart))
        .toList();
    final monthlyPaymentAmount =
        monthlyPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    return {
      'totalPaidAmount': totalPaidAmount,
      'totalPaidCount': totalPaidCount,
      'paymentRate': paymentRate,
      'todayPayments': {
        'count': todayPayments.length,
        'amount': todayPaymentAmount
      },
      'yesterdayPayments': {
        'count': yesterdayPayments.length,
        'amount': yesterdayPaymentAmount
      },
      'weeklyPayments': {
        'count': weeklyPayments.length,
        'amount': weeklyPaymentAmount
      },
      'monthlyPayments': {
        'count': monthlyPayments.length,
        'amount': monthlyPaymentAmount
      },
    };
  }

  // محتوى إحصائيات السداد
  Widget _buildPaymentStatisticsContent() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        if (customerDebts.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment_outlined,
                  size: 80,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد بيانات سداد بعد',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'ستظهر إحصائيات السداد هنا عند وجود مدفوعات',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return FutureBuilder<Map<String, dynamic>>(
          future: _getCustomerPaymentStats(customerDebts),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const Center(child: CircularProgressIndicator());
            }

            final stats = snapshot.data!;
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // بطاقة إجمالي المدفوعات
                  _buildStatCard(
                    'إجمالي المدفوعات',
                    NumberFormatter.formatCurrency(
                      (stats['totalPaidAmount'] ?? 0.0).toInt(),
                    ),
                    Icons.payments,
                    Colors.green,
                    'د.ع',
                    subtitle: '${stats['totalPaidCount']} كارت مسدد',
                  ),
                  const SizedBox(height: 16),

                  // سداد اليوم
                  _buildStatCard(
                    'سداد اليوم',
                    NumberFormatter.formatCurrency(
                      (stats['todayPayments']['amount'] ?? 0.0).toInt(),
                    ),
                    Icons.today,
                    Colors.blue,
                    'د.ع',
                    subtitle: '${stats['todayPayments']['count']} كارت',
                  ),
                  const SizedBox(height: 12),

                  // سداد الأمس
                  _buildStatCard(
                    'سداد الأمس',
                    NumberFormatter.formatCurrency(
                      (stats['yesterdayPayments']['amount'] ?? 0.0).toInt(),
                    ),
                    Icons.history,
                    Colors.teal,
                    'د.ع',
                    subtitle: '${stats['yesterdayPayments']['count']} كارت',
                  ),
                  const SizedBox(height: 12),

                  // سداد الأسبوع
                  _buildStatCard(
                    'سداد الأسبوع',
                    NumberFormatter.formatCurrency(
                      (stats['weeklyPayments']['amount'] ?? 0.0).toInt(),
                    ),
                    Icons.date_range,
                    Colors.orange,
                    'د.ع',
                    subtitle: '${stats['weeklyPayments']['count']} كارت',
                  ),
                  const SizedBox(height: 12),

                  // سداد الشهر
                  _buildStatCard(
                    'سداد الشهر',
                    NumberFormatter.formatCurrency(
                      (stats['monthlyPayments']['amount'] ?? 0.0).toInt(),
                    ),
                    Icons.calendar_month,
                    Colors.purple,
                    'د.ع',
                    subtitle: '${stats['monthlyPayments']['count']} كارت',
                  ),
                  const SizedBox(height: 16),

                  // معدل السداد
                  _buildStatCard(
                    'معدل السداد',
                    '${stats['paymentRate'].toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.cyan,
                    '',
                    subtitle: 'من إجمالي الديون',
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // جلب إحصائيات السداد للعميل
  Future<Map<String, dynamic>> _getCustomerPaymentStats(
      List<Debt> customerDebts) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final weekStart = today.subtract(Duration(days: today.weekday - 1));
      final monthStart = DateTime(today.year, today.month);

      // الديون المسددة
      final paidDebts = customerDebts
          .where((debt) => debt.status == DebtStatus.paid)
          .toList();

      final totalPaidAmount =
          paidDebts.fold<double>(0, (sum, debt) => sum + debt.amount);
      final totalAmount =
          customerDebts.fold<double>(0, (sum, debt) => sum + debt.amount);

      // سداد اليوم
      final todayPaidDebts = paidDebts.where((debt) {
        final debtDay = DateTime(
            debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
        return debtDay.isAtSameMomentAs(today);
      }).toList();

      // سداد الأمس
      final yesterdayPaidDebts = paidDebts.where((debt) {
        final debtDay = DateTime(
            debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
        return debtDay.isAtSameMomentAs(yesterday);
      }).toList();

      // سداد الأسبوع
      final weeklyPaidDebts = paidDebts
          .where((debt) => debt.entryDate
              .isAfter(weekStart.subtract(const Duration(days: 1))))
          .toList();

      // سداد الشهر
      final monthlyPaidDebts = paidDebts
          .where((debt) => debt.entryDate
              .isAfter(monthStart.subtract(const Duration(days: 1))))
          .toList();

      // حساب معدل السداد
      final paymentRate =
          totalAmount > 0 ? (totalPaidAmount / totalAmount) * 100 : 0.0;

      return {
        'totalPaidAmount': totalPaidAmount,
        'totalPaidCount': paidDebts.length,
        'paymentRate': paymentRate,
        'todayPayments': {
          'count': todayPaidDebts.length,
          'amount':
              todayPaidDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
        },
        'yesterdayPayments': {
          'count': yesterdayPaidDebts.length,
          'amount': yesterdayPaidDebts.fold<double>(
              0, (sum, debt) => sum + debt.amount),
        },
        'weeklyPayments': {
          'count': weeklyPaidDebts.length,
          'amount':
              weeklyPaidDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
        },
        'monthlyPayments': {
          'count': monthlyPaidDebts.length,
          'amount': monthlyPaidDebts.fold<double>(
              0, (sum, debt) => sum + debt.amount),
        },
      };
    } catch (e) {
      return {
        'totalPaidAmount': 0.0,
        'totalPaidCount': 0,
        'paymentRate': 0.0,
        'todayPayments': {'count': 0, 'amount': 0.0},
        'yesterdayPayments': {'count': 0, 'amount': 0.0},
        'weeklyPayments': {'count': 0, 'amount': 0.0},
        'monthlyPayments': {'count': 0, 'amount': 0.0},
      };
    }
  }
}

// كلاس منفصل لشاشة الإحصائيات مع التبويبات
class _StatisticsTabScreen extends StatefulWidget {
  const _StatisticsTabScreen({
    required this.customer,
  });
  final Customer customer;

  @override
  State<_StatisticsTabScreen> createState() => _StatisticsTabScreenState();
}

class _StatisticsTabScreenState extends State<_StatisticsTabScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإحصائيات'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
          tabs: const [
            Tab(
              icon: Icon(Icons.analytics_outlined),
              text: 'إحصائيات الديون',
            ),
            Tab(
              icon: Icon(Icons.payment),
              text: 'إحصائيات السداد',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _StatisticsContent(customer: widget.customer),
          _PaymentStatisticsContent(customer: widget.customer),
        ],
      ),
    );
  }
}

// كلاس منفصل لمحتوى إحصائيات الديون
class _StatisticsContent extends StatelessWidget {
  const _StatisticsContent({required this.customer});
  final Customer customer;

  // فتح صفحة الديون المتأخرة
  void _showOverdueDebtsDialog(BuildContext context, List<Debt> overdueDebts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OverdueDebtsScreen(
          customer: customer,
          overdueDebts: overdueDebts,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CardTypeProvider>(
      builder: (context, cardTypeProvider, child) {
        return FutureBuilder<List<Debt>>(
          key: ValueKey(
            'statistics_content_${customer.id}_${DateTime.now().millisecondsSinceEpoch}',
          ),
          future: Future.value(
            Provider.of<DebtProvider>(context, listen: false)
                .debts
                .where((debt) => debt.customerId == customer.id)
                .toList(),
          ),
          builder: (context, snapshot) {
            final customerDebts = snapshot.data ?? [];

            if (customerDebts.isEmpty) {
              return const Center(
                child: Text(
                  'لا توجد ديون لهذا العميل',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              );
            }

            final now = DateTime.now();
            final today = DateTime(now.year, now.month, now.day);
            final yesterday = today.subtract(const Duration(days: 1));
            final weekStart = today.subtract(Duration(days: today.weekday - 1));
            final monthStart = DateTime(now.year, now.month);
            final last30Days = today.subtract(const Duration(days: 30));

            // حسابات اليوم (المبيعات فقط - ديون العملاء لك)
            final todayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(today) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final todaySales = todayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final todayCount =
                todayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الأمس (المبيعات فقط - ديون العملاء لك)
            final yesterdayDebts = customerDebts.where((debt) {
              final debtDate = DateTime(
                debt.entryDate.year,
                debt.entryDate.month,
                debt.entryDate.day,
              );
              return debtDate.isAtSameMomentAs(yesterday) &&
                  debt.direction == DebtDirection.customerOwesMe;
            }).toList();
            final yesterdaySales = yesterdayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final yesterdayCount =
                yesterdayDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الأسبوع (المبيعات فقط - ديون العملاء لك)
            final weekDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(weekStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final weekSales = weekDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final weekCount =
                weekDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // حسابات الشهر (المبيعات فقط - ديون العملاء لك)
            final monthDebts = customerDebts
                .where((debt) =>
                    debt.entryDate.isAfter(monthStart) &&
                    debt.direction == DebtDirection.customerOwesMe)
                .toList();
            final monthSales = monthDebts.fold(
              0.0,
              (sum, debt) => sum + debt.amount,
            );
            final monthCount =
                monthDebts.fold(0, (sum, debt) => sum + debt.quantity);

            // نشاط العميل (آخر 30 يوم)
            final recentDebts = customerDebts
                .where((debt) => debt.entryDate.isAfter(last30Days))
                .toList();
            final activityPercentage = customerDebts.isNotEmpty
                ? (recentDebts.length / customerDebts.length * 100)
                : 0.0;

            // إحصائيات أنواع الكروت مع المبالغ والكمية الصحيحة
            final cardTypeStats = <String, Map<String, dynamic>>{};
            for (final debt in customerDebts) {
              if (!cardTypeStats.containsKey(debt.cardType)) {
                cardTypeStats[debt.cardType] = {
                  'count': 0,
                  'amount': 0.0,
                  'quantity': 0,
                };
              }
              cardTypeStats[debt.cardType]!['count'] =
                  (cardTypeStats[debt.cardType]!['count'] as int) + 1;
              cardTypeStats[debt.cardType]!['amount'] =
                  (cardTypeStats[debt.cardType]!['amount'] as double) +
                      debt.amount;
              cardTypeStats[debt.cardType]!['quantity'] =
                  (cardTypeStats[debt.cardType]!['quantity'] as int) +
                      debt.quantity;
            }
            final sortedCardTypes = cardTypeStats.entries.toList()
              ..sort(
                (a, b) => (b.value['count'] as int).compareTo(
                  a.value['count'] as int,
                ),
              );

            // الديون المتأخرة
            final overdueDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isBefore(today);
            }).toList();
            final overdueAmount = overdueDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            // الديون المستحقة اليوم
            final dueTodayDebts = customerDebts.where((debt) {
              if (debt.status == DebtStatus.paid) return false;
              final dueDate = DateTime(
                debt.dueDate.year,
                debt.dueDate.month,
                debt.dueDate.day,
              );
              return dueDate.isAtSameMomentAs(today);
            }).toList();
            final dueTodayAmount = dueTodayDebts.fold(
              0.0,
              (sum, debt) => sum + debt.remainingAmount,
            );

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم أنواع الكروت
                  if (sortedCardTypes.isNotEmpty) ...[
                    const Text(
                      'أنواع الكروت',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // بطاقة إجمالي الكارتات الشاملة
                    _buildComprehensiveCardSummary(
                      sortedCardTypes,
                      cardTypeProvider,
                    ),

                    const SizedBox(height: 16),

                    // عرض أنواع الكروت في عمود
                    Column(
                      children: sortedCardTypes.map((cardTypeEntry) {
                        final cardTypeId = cardTypeEntry.key;
                        final cardTypeData = cardTypeEntry.value;

                        final amount = cardTypeData['amount'] as double;
                        final quantity = cardTypeData['quantity'] as int;

                        // تحويل نوع الكارت إلى اسم عربي أولاً
                        final String cardTypeName = _getCardTypeDisplayName(
                          cardTypeId,
                          cardTypeProvider,
                        );

                        // تحديد اللون بناءً على نوع الكارت
                        MaterialColor color;
                        if (cardTypeName == 'زين') {
                          color = Colors.purple; // بنفسجي فاتح لزين
                        } else if (cardTypeName == 'أبو العشرة') {
                          color = Colors.cyan; // فايروسي فاتح لأبو العشرة
                        } else if (cardTypeName == 'آسيا') {
                          color = Colors.red;
                        } else if (cardTypeName == 'أبو الستة') {
                          color = Colors.grey;
                        } else {
                          // للأنواع الأخرى، استخدم الألوان الافتراضية
                          final colors = [
                            Colors.orange,
                            Colors.blue,
                            Colors.green,
                            Colors.teal,
                            Colors.indigo,
                          ];
                          final colorIndex =
                              sortedCardTypes.indexOf(cardTypeEntry);
                          color = colors[colorIndex % colors.length];
                        }

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: _buildCardTypeStatCard(
                            cardTypeName,
                            quantity,
                            amount,
                            color,
                            customerDebts.length,
                            customerDebts.fold(
                                0, (sum, debt) => sum + debt.quantity),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 20),
                  ],

                  // الصف الأول - مبيعات اليوم والأمس
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _navigateToCustomerTodayDebts(context),
                          child: _buildStatCard(
                            'مبيعات اليوم',
                            _formatNumber(todaySales),
                            Icons.today,
                            Colors.blue,
                            'د.ع',
                            subtitle: '$todayCount كارت',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: GestureDetector(
                          onTap: () =>
                              _navigateToCustomerYesterdayDebts(context),
                          child: _buildStatCard(
                            'مبيعات الأمس',
                            _formatNumber(yesterdaySales),
                            Icons.history,
                            Colors.teal,
                            'د.ع',
                            subtitle: '$yesterdayCount كارت',
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // الصف الثاني - مبيعات الأسبوع والشهر
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الأسبوع',
                          _formatNumber(weekSales),
                          Icons.date_range,
                          Colors.green,
                          'د.ع',
                          subtitle: '$weekCount كارت',
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'مبيعات الشهر',
                          _formatNumber(monthSales),
                          Icons.calendar_month,
                          Colors.purple,
                          'د.ع',
                          subtitle: '$monthCount كارت',
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // بطاقة نشاط العميل - عريضة
                  _buildStatCard(
                    'نشاط العميل',
                    activityPercentage.toStringAsFixed(1),
                    Icons.trending_up,
                    Colors.cyan,
                    '%',
                    subtitle: 'آخر 30 يوم',
                  ),

                  const SizedBox(height: 12),

                  // الصف الرابع - الديون المتأخرة والمستحقة اليوم
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => OverdueDebtsScreen(
                                  customer: customer,
                                  overdueDebts: overdueDebts,
                                ),
                              ),
                            );
                          },
                          child: _buildStatCard(
                            'ديون متأخرة',
                            _formatNumber(overdueAmount),
                            Icons.warning,
                            Colors.red,
                            'د.ع',
                            subtitle:
                                '${overdueDebts.fold(0, (sum, debt) => sum + debt.quantity)} كارت',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'استحقاق اليوم',
                          _formatNumber(dueTodayAmount),
                          Icons.schedule,
                          Colors.amber,
                          'د.ع',
                          subtitle:
                              '${dueTodayDebts.fold(0, (sum, debt) => sum + debt.quantity)} كارت',
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // دالة تنسيق الأرقام مع فاصل الآلاف
  String _formatNumber(double number) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(number.round());
  }

  // التنقل إلى ديون العميل اليوم
  void _navigateToCustomerTodayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: customer,
          isToday: true,
          title: 'ديون اليوم',
        ),
      ),
    );
  }

  // التنقل إلى ديون العميل الأمس
  void _navigateToCustomerYesterdayDebts(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDailyDebtsScreen(
          customer: customer,
          isToday: false,
          title: 'ديون الأمس',
        ),
      ),
    );
  }

  // تحويل نوع الكارت إلى اسم عربي
  String _getCardTypeDisplayName(
      String cardTypeId, CardTypeProvider cardTypeProvider) {
    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardTypeId,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardTypeId,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardTypeId;
      }
    }
  }

  // بناء بطاقة إجمالي الكارتات الشاملة
  Widget _buildComprehensiveCardSummary(
    List<MapEntry<String, Map<String, dynamic>>> sortedCardTypes,
    CardTypeProvider cardTypeProvider,
  ) {
    // حساب الإجماليات
    final totalQuantity = sortedCardTypes.fold<int>(
      0,
      (sum, entry) => sum + ((entry.value['quantity'] as int?) ?? 0),
    );

    final totalAmount = sortedCardTypes.fold<double>(
      0,
      (sum, entry) => sum + ((entry.value['amount'] as double?) ?? 0),
    );

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1E3A8A).withValues(alpha: 0.1),
            const Color(0xFF3B82F6).withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان الرئيسي
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1E3A8A), Color(0xFF3B82F6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.credit_card,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الكارتات',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1E3A8A),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'ملخص شامل لجميع أنواع الكروت',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF64748B),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // بطاقة إجمالي المبلغ - ارتفاع أقل
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
                horizontal: 20, vertical: 12), // تقليل من 16 إلى 12
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.grey.shade300, // تخفيف من shade400 إلى shade300
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.8),
                  blurRadius: 6,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                // الأيقونة في الوسط - أصغر
                Container(
                  padding: const EdgeInsets.all(8), // تقليل من 12 إلى 8
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius:
                        BorderRadius.circular(12), // تقليل من 14 إلى 12
                    border: Border.all(
                      color: Colors.grey.shade300,
                    ),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: Colors.grey.shade700,
                    size: 24, // تقليل من 28 إلى 24
                  ),
                ),
                const SizedBox(height: 8), // تقليل من 12 إلى 8
                // المبلغ تحت الأيقونة
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      NumberFormatter.formatCurrency(totalAmount.toInt()),
                      style: const TextStyle(
                        fontSize: 20, // تقليل من 22 إلى 20
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 4), // تقليل من 6 إلى 4
                    Text(
                      'د.ع',
                      style: TextStyle(
                        fontSize: 12, // تقليل من 14 إلى 12
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4), // تقليل من 6 إلى 4
                // العنوان تحت المبلغ بحجم صغير
                Text(
                  'إجمالي المبلغ',
                  style: TextStyle(
                    fontSize: 10, // تقليل من 12 إلى 10
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // الصف الثاني: إجمالي الكارت وأنواع الكارت متقابلتين - ارتفاع أقل
          Row(
            children: [
              // إجمالي الكارت
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12), // تقليل من 16 إلى 12
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors
                          .grey.shade300, // تخفيف من shade400 إلى shade300
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.15),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8), // تقليل من 12 إلى 8
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius:
                              BorderRadius.circular(12), // تقليل من 14 إلى 12
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        child: Icon(
                          Icons.inventory_2,
                          color: Colors.grey.shade700,
                          size: 24, // تقليل من 28 إلى 24
                        ),
                      ),
                      const SizedBox(height: 8), // تقليل من 12 إلى 8
                      Text(
                        NumberFormatter.formatNumber(totalQuantity),
                        style: const TextStyle(
                          fontSize: 20, // تقليل من 22 إلى 20
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4), // تقليل من 6 إلى 4
                      Text(
                        'إجمالي الكارت',
                        style: TextStyle(
                          fontSize: 11, // تقليل من 13 إلى 11
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // أنواع الكارت
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12), // تقليل من 16 إلى 12
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors
                          .grey.shade300, // تخفيف من shade400 إلى shade300
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.15),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8), // تقليل من 12 إلى 8
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius:
                              BorderRadius.circular(12), // تقليل من 14 إلى 12
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        child: Icon(
                          Icons.category_outlined,
                          color: Colors.grey.shade700,
                          size: 24, // تقليل من 28 إلى 24
                        ),
                      ),
                      const SizedBox(height: 8), // تقليل من 12 إلى 8
                      Text(
                        '${sortedCardTypes.length}',
                        style: const TextStyle(
                          fontSize: 20, // تقليل من 22 إلى 20
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4), // تقليل من 6 إلى 4
                      Text(
                        'أنواع الكارت',
                        style: TextStyle(
                          fontSize: 11, // تقليل من 13 إلى 11
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائيات نوع كارت
  Widget _buildCardTypeStatCard(
    String cardTypeName,
    int quantity,
    double amount,
    MaterialColor color,
    int totalDebts,
    int totalQuantity,
  ) {
    final percentage =
        totalQuantity > 0 ? (quantity / totalQuantity * 100) : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة ولون نوع الكارت
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.shade400, color.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Icon(
              Icons.credit_card,
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // معلومات نوع الكارت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cardTypeName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87, // تغيير إلى أسود
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${NumberFormatter.formatCurrency(amount.toInt())} د.ع',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87, // تغيير إلى أسود
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                // شريط التقدم
                Container(
                  height: 6,
                  decoration: BoxDecoration(
                    color: color.shade100,
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: percentage / 100,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [color.shade400, color.shade600],
                        ),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // الإحصائيات
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                NumberFormatter.formatNumber(quantity),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87, // تغيير إلى أسود
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: color.shade200),
                ),
                child: Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87, // تغيير إلى أسود
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(12), // تقليل padding من 16 إلى 12
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, color.shade50.withValues(alpha: 0.3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12), // تقليل من 16 إلى 12
        border: Border.all(color: color.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8, // تقليل من 12 إلى 8
            offset: const Offset(0, 3), // تقليل من 4 إلى 3
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7),
            blurRadius: 6, // تقليل من 8 إلى 6
            offset: const Offset(-2, -2), // تقليل من -3 إلى -2
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title Row
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8), // تقليل من 10 إلى 8
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [color.shade400, color.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(10), // تقليل من 12 إلى 10
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 6, // تقليل من 8 إلى 6
                      offset: const Offset(0, 2), // تقليل من 3 إلى 2
                    ),
                  ],
                ),
                child: Icon(icon,
                    color: Colors.white, size: 16), // تقليل من 20 إلى 16
              ),
              const SizedBox(width: 8), // تقليل من 12 إلى 8
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12, // تقليل من 14 إلى 12
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12), // تقليل من 16 إلى 12

          // Value Section
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Flexible(
                // إضافة Flexible لمنع overflow
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 20, // تقليل من 28 إلى 20
                    fontWeight: FontWeight.bold,
                    color: color.shade700,
                    height: 1.0,
                  ),
                  overflow:
                      TextOverflow.ellipsis, // إضافة ellipsis للنصوص الطويلة
                ),
              ),
              if (unit.isNotEmpty) ...[
                const SizedBox(width: 3), // تقليل من 4 إلى 3
                Text(
                  unit,
                  style: TextStyle(
                    fontSize: 12, // تقليل من 14 إلى 12
                    fontWeight: FontWeight.w600,
                    color: color.shade600,
                  ),
                ),
              ],
            ],
          ),

          if (subtitle != null) ...[
            const SizedBox(height: 6), // تقليل من 8 إلى 6
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10, // تقليل من 12 إلى 10
                color: color.shade600,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis, // إضافة ellipsis
            ),
          ],
        ],
      ),
    );
  }
}

// كلاس منفصل لمحتوى إحصائيات السداد
class _PaymentStatisticsContent extends StatelessWidget {
  const _PaymentStatisticsContent({required this.customer});
  final Customer customer;

  @override
  Widget build(BuildContext context) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        return FutureBuilder<Map<String, dynamic>>(
          future: _calculatePaymentStatistics(debtProvider),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text(
                  'خطأ في تحميل الإحصائيات: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              );
            }

            final stats = snapshot.data!;
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // بطاقة إجمالي المدفوعات - تصميم مخصص
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12), // تقليل من 16 إلى 12
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.shade300, // حواف رمادي خفيف
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // أيقونة في الوسط - أصغر
                        Container(
                          padding: const EdgeInsets.all(8), // تقليل من 10 إلى 8
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius:
                                BorderRadius.circular(10), // تقليل من 12 إلى 10
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          child: Icon(
                            Icons.payments,
                            color: Colors.grey.shade700,
                            size: 20, // تقليل من 24 إلى 20
                          ),
                        ),
                        const SizedBox(height: 10), // تقليل من 12 إلى 10
                        // القيمة في الوسط
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Text(
                              NumberFormatter.formatCurrency(
                                (stats['totalPaidAmount'] ?? 0.0).toInt(),
                              ),
                              style: const TextStyle(
                                fontSize: 20, // تقليل من 22 إلى 20
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(width: 4), // تقليل من 6 إلى 4
                            Text(
                              'د.ع',
                              style: TextStyle(
                                fontSize: 12, // تقليل من 14 إلى 12
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6), // تقليل من 8 إلى 6
                        // عنوان البطاقة في الوسط
                        Text(
                          'إجمالي المدفوعات',
                          style: TextStyle(
                            fontSize: 13, // تقليل من 14 إلى 13
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 3), // تقليل من 4 إلى 3
                        // النص الفرعي في الوسط
                        Text(
                          '${stats['totalPaidCount']} كارت مسدد',
                          style: TextStyle(
                            fontSize: 11, // تقليل من 12 إلى 11
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // الشبكة للبطاقات الأخرى
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2, // عمودين
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 1.1, // نسبة العرض للارتفاع
                    children: [
                      // سداد اليوم
                      _buildStatCard(
                        'سداد اليوم',
                        NumberFormatter.formatCurrency(
                          (stats['todayPayments']['amount'] ?? 0.0).toInt(),
                        ),
                        Icons.today,
                        Colors.blue,
                        'د.ع',
                        subtitle: '${stats['todayPayments']['count']} كارت',
                      ),

                      // سداد الأمس
                      _buildStatCard(
                        'سداد الأمس',
                        NumberFormatter.formatCurrency(
                          (stats['yesterdayPayments']['amount'] ?? 0.0).toInt(),
                        ),
                        Icons.history,
                        Colors.teal,
                        'د.ع',
                        subtitle: '${stats['yesterdayPayments']['count']} كارت',
                      ),

                      // سداد الأسبوع
                      _buildStatCard(
                        'سداد الأسبوع',
                        NumberFormatter.formatCurrency(
                          (stats['weeklyPayments']['amount'] ?? 0.0).toInt(),
                        ),
                        Icons.date_range,
                        Colors.green,
                        'د.ع',
                        subtitle: '${stats['weeklyPayments']['count']} كارت',
                      ),

                      // سداد الشهر
                      _buildStatCard(
                        'سداد الشهر',
                        NumberFormatter.formatCurrency(
                          (stats['monthlyPayments']['amount'] ?? 0.0).toInt(),
                        ),
                        Icons.calendar_month,
                        Colors.purple,
                        'د.ع',
                        subtitle: '${stats['monthlyPayments']['count']} كارت',
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // معدل السداد - منفصل
                  _buildStatCard(
                    'معدل السداد',
                    '${stats['paymentRate'].toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.cyan,
                    '',
                    subtitle: 'من إجمالي الديون',
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // فتح صفحة الديون المتأخرة
  void _showOverdueDebtsDialog(BuildContext context, List<Debt> overdueDebts) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OverdueDebtsScreen(
          customer: customer,
          overdueDebts: overdueDebts,
        ),
      ),
    );
  }

  // حساب إحصائيات السداد
  Future<Map<String, dynamic>> _calculatePaymentStatistics(
      DebtProvider debtProvider) async {
    final customerDebts = debtProvider.debts
        .where((debt) => debt.customerId == customer.id)
        .toList();

    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == customer.id)
        .toList();

    if (customerDebts.isEmpty) {
      return {
        'totalPaidAmount': 0.0,
        'totalPaidCount': 0,
        'paymentRate': 0.0,
        'todayPayments': {'count': 0, 'amount': 0.0},
        'yesterdayPayments': {'count': 0, 'amount': 0.0},
        'weeklyPayments': {'count': 0, 'amount': 0.0},
        'monthlyPayments': {'count': 0, 'amount': 0.0},
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final monthStart = DateTime(now.year, now.month);

    // إجمالي المدفوعات
    final totalPaidAmount =
        customerDebts.fold(0.0, (sum, debt) => sum + debt.paidAmount);
    final totalPaidCount =
        customerDebts.where((debt) => debt.paidAmount > 0).length;

    // معدل السداد
    final totalAmount =
        customerDebts.fold(0.0, (sum, debt) => sum + debt.amount);
    final paymentRate =
        totalAmount > 0 ? (totalPaidAmount / totalAmount * 100) : 0.0;

    // سداد اليوم
    final todayPayments = customerPayments.where((payment) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );
      return paymentDate.isAtSameMomentAs(today);
    }).toList();

    final todayPaymentAmount =
        todayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الأمس
    final yesterdayPayments = customerPayments.where((payment) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );
      return paymentDate.isAtSameMomentAs(yesterday);
    }).toList();

    final yesterdayPaymentAmount =
        yesterdayPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الأسبوع
    final weeklyPayments = customerPayments
        .where((payment) => payment.paymentDate.isAfter(weekStart))
        .toList();
    final weeklyPaymentAmount =
        weeklyPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    // سداد الشهر
    final monthlyPayments = customerPayments
        .where((payment) => payment.paymentDate.isAfter(monthStart))
        .toList();
    final monthlyPaymentAmount =
        monthlyPayments.fold(0.0, (sum, payment) => sum + payment.amount);

    return {
      'totalPaidAmount': totalPaidAmount,
      'totalPaidCount': totalPaidCount,
      'paymentRate': paymentRate,
      'todayPayments': {
        'count': todayPayments.length,
        'amount': todayPaymentAmount
      },
      'yesterdayPayments': {
        'count': yesterdayPayments.length,
        'amount': yesterdayPaymentAmount
      },
      'weeklyPayments': {
        'count': weeklyPayments.length,
        'amount': weeklyPaymentAmount
      },
      'monthlyPayments': {
        'count': monthlyPayments.length,
        'amount': monthlyPaymentAmount
      },
    };
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, color.shade50.withValues(alpha: 0.3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7),
            blurRadius: 6,
            offset: const Offset(-2, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title Row
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [color.shade400, color.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 16),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Value Section
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Flexible(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color.shade700,
                    height: 1.0,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (unit.isNotEmpty) ...[
                const SizedBox(width: 3),
                Text(
                  unit,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color.shade600,
                  ),
                ),
              ],
            ],
          ),

          if (subtitle != null) ...[
            const SizedBox(height: 6),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: color.shade600,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
