import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../screens/payment_statistics_screen.dart';
import 'payment_card.dart';

// أنواع عرض التسديدات
enum PaymentCardViewType {
  standard, // العرض العادي
  compact, // عرض مضغوط
  mini, // عرض مصغر (مثل قائمة الديون)
  detailed, // عرض مفصل
  grid, // عرض شبكي
  timeline, // عرض زمني
}

class PaymentsTab extends StatefulWidget {
  const PaymentsTab({
    super.key,
    required this.customer,
    this.isSelectionMode = false,
    this.selectedPaymentIds = const {},
    this.onSelectionChanged,
    this.currentViewType,
  });
  final Customer customer;
  final bool isSelectionMode;
  final Set<int> selectedPaymentIds;
  final Function(int paymentId, bool isSelected)? onSelectionChanged;
  final PaymentCardViewType? currentViewType;

  @override
  State<PaymentsTab> createState() => _PaymentsTabState();
}

class _PaymentsTabState extends State<PaymentsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};
  PaymentCardViewType _currentViewType = PaymentCardViewType.standard;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // إجبار استخدام العرض المصغر فقط
    _currentViewType = PaymentCardViewType.mini;
    debugPrint(
        'PaymentsTab initState - تم إجبار نوع العرض على المصغر: ${_currentViewType.name}');

    // استخدام نوع العرض الممرر من الخارج إذا كان متوفراً (لكن سيتم إجباره على المصغر)
    if (widget.currentViewType != null) {
      _currentViewType = PaymentCardViewType.mini; // إجبار العرض المصغر
      debugPrint(
          'PaymentsTab initState - تم إجبار نوع العرض على المصغر: ${_currentViewType.name}');
    }

    // Load payments when tab is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });

    // تحديث العداد كل دقيقة لدقة العرض (معطل مؤقتاً)
    // _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
    //   if (mounted) {
    //     setState(() {});
    //   }
    // });
  }

  @override
  void didUpdateWidget(PaymentsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // تحديث نوع العرض عند تغييره من الخارج
    if (widget.currentViewType != null &&
        widget.currentViewType != oldWidget.currentViewType) {
      debugPrint(
          'تحديث نوع العرض في PaymentsTab من ${oldWidget.currentViewType?.name} إلى ${widget.currentViewType!.name}');
      setState(() {
        _currentViewType = widget.currentViewType!;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload payments when dependencies change (e.g., when returning from payment dialog)
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.loadCustomerPayments(widget.customer.id!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    debugPrint(
        'PaymentsTab build - نوع العرض الحالي: ${_currentViewType.name}');
    debugPrint(
        'PaymentsTab build - نوع العرض من الخارج: ${widget.currentViewType?.name ?? "null"}');

    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Get all payments for this customer
        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        debugPrint(
          'PaymentsTab: Total payments in provider: ${debtProvider.payments.length}',
        );
        debugPrint(
          'PaymentsTab: Customer ${widget.customer.id} payments: ${customerPayments.length}',
        );

        if (customerPayments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.payment_outlined, size: 80, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد تسديدات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر التسديدات هنا بعد إجراء عمليات الدفع',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // شريط العرض (فقط عندما لا نكون في وضع التحديد)
            if (!_isSelectionMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // عنوان القسم
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.payment,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'قائمة التسديدات (${customerPayments.length})',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر الإحصائيات
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      child: TextButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PaymentStatisticsScreen(
                                  customer: widget.customer),
                            ),
                          );
                        },
                        icon: const Icon(Icons.analytics, size: 18),
                        label: const Text(
                          'إحصائيات',
                          style: TextStyle(
                              fontSize: 13, fontWeight: FontWeight.w600),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: const Color(0xFF00695C),
                          backgroundColor:
                              const Color(0xFF00695C).withValues(alpha: 0.1),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // شريط حالة السداد (اليوم وأمس)
            if (!_isSelectionMode && customerPayments.isNotEmpty)
              _buildPaymentStatusStrip(customerPayments),

            // Action buttons
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Top row - Selection and Statistics buttons
                  if (customerPayments.isNotEmpty) ...[
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _isSelectionMode
                          ? Row(
                              key: const ValueKey('selection_mode'),
                              children: [
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () => _toggleSelectionMode(),
                                    icon: const Icon(Icons.close, size: 18),
                                    label: const Text(
                                      'إلغاء التحديد',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.red,
                                      side: BorderSide(
                                        color: Colors.red.shade300,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _selectedPaymentIds.isEmpty
                                        ? null
                                        : () => _deleteSelectedPayments(),
                                    icon: const Icon(Icons.delete, size: 18),
                                    label: Text(
                                      'حذف المحدد (${_selectedPaymentIds.length})',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          _selectedPaymentIds.isEmpty
                                              ? Colors.grey[400]
                                              : Colors.red[600],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _deleteAllPayments(),
                                    icon: const Icon(
                                      Icons.delete_forever,
                                      size: 18,
                                    ),
                                    label: const Text(
                                      'حذف الكل',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red[700],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    const SizedBox(height: 8),
                  ],
                ],
              ),
            ),

            // Payments List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerPayments(widget.customer.id!);
                },
                child: _buildPaymentsView(customerPayments, debtProvider),
              ),
            ),
          ],
        );
      },
    );
  }

  // بناء شريط حالة السداد (اليوم وأمس)
  Widget _buildPaymentStatusStrip(List customerPayments) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // حساب التسديدات حسب التاريخ
    int todayPayments = 0;
    int yesterdayPayments = 0;

    for (final payment in customerPayments) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );

      if (paymentDate.isAtSameMomentAs(today)) {
        todayPayments++;
      } else if (paymentDate.isAtSameMomentAs(yesterday)) {
        yesterdayPayments++;
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF00695C).withValues(alpha: 0.1),
            const Color(0xFF00695C).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF00695C).withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // أيقونة التسديدات
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: const Color(0xFF00695C).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.schedule,
              color: Color(0xFF00695C),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),

          // تسديدات اليوم
          Expanded(
            child: Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: todayPayments > 0
                        ? const Color(0xFF00695C).withValues(alpha: 0.15)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: todayPayments > 0
                          ? const Color(0xFF00695C).withValues(alpha: 0.3)
                          : Colors.grey.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.today,
                        size: 14,
                        color: todayPayments > 0
                            ? const Color(0xFF00695C)
                            : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'اليوم: $todayPayments',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: todayPayments > 0
                              ? const Color(0xFF00695C)
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // تسديدات أمس
          Expanded(
            child: Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: yesterdayPayments > 0
                        ? const Color(0xFF00695C).withValues(alpha: 0.15)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: yesterdayPayments > 0
                          ? const Color(0xFF00695C).withValues(alpha: 0.3)
                          : Colors.grey.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.history,
                        size: 14,
                        color: yesterdayPayments > 0
                            ? const Color(0xFF00695C)
                            : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'أمس: $yesterdayPayments',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: yesterdayPayments > 0
                              ? const Color(0xFF00695C)
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void _deleteSelectedPayments() {
    if (_selectedPaymentIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.delete, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف التسديدات المحددة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف ${_selectedPaymentIds.length} تسديد محدد نهائياً؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );

                final selectedCount = _selectedPaymentIds.length;
                for (final paymentId in _selectedPaymentIds) {
                  await debtProvider.deletePayment(paymentId);
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف $selectedCount تسديد نهائياً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائياً',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (customerPayments.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف جميع التسديدات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف جميع التسديدات (${customerPayments.length} تسديد) نهائياً؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final totalCount = customerPayments.length;
                for (final payment in customerPayments) {
                  if (payment.id != null) {
                    await debtProvider.deletePayment(payment.id!);
                  }
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف $totalCount تسديد نهائياً'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائياً',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء بطاقات التصنيف الزمني
  Widget _buildTimeClassificationCards(List customerPayments, debtProvider) {
    // تنظيم التسديدات حسب الفترات الزمنية
    final organizedPayments = _organizePaymentsByDate(customerPayments);

    if (organizedPayments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: organizedPayments.entries.map((entry) {
        final category = entry.key;
        final payments = entry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة عنوان الفئة الزمنية (مثل بطاقات الديون)
            Container(
              width: double.infinity,
              margin:
                  const EdgeInsets.only(bottom: 4, top: 16, left: 8, right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _getPaymentCategoryColors(category),
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: _getPaymentCategoryColors(category)[0]
                        .withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // أيقونة الفئة
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getPaymentCategoryIcon(category),
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),

                  // اسم الفئة
                  Expanded(
                    child: Text(
                      category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // إحصائيات الفئة - الكمية والمبلغ جنباً إلى جنب
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // الكمية - بطاقة احترافية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'الكمية:',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${_getTotalQuantity(payments)}',
                              style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 16), // فاصل بين الكمية والمبلغ

                      // المبلغ - بطاقة احترافية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'المبلغ:',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _formatCurrencyWithoutSymbol(
                                  _getTotalAmount(payments)),
                              style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // بطاقات التسديدات مع بطاقة التاريخ فوق كل واحدة
            _buildCategoryPaymentsList(payments, debtProvider, category),

            const SizedBox(height: 4),
          ],
        );
      }).toList(),
    );
  }

  // دالة لتنظيم التسديدات حسب التاريخ
  Map<String, List> _organizePaymentsByDate(List customerPayments) {
    debugPrint('تنظيم التسديدات: عدد التسديدات = ${customerPayments.length}');
    final Map<String, List> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisWeek = today.subtract(Duration(days: today.weekday - 1));
    final thisMonth = DateTime(now.year, now.month);

    for (final payment in customerPayments) {
      final paymentDate = DateTime(
        payment.paymentDate.year,
        payment.paymentDate.month,
        payment.paymentDate.day,
      );

      String category;
      if (paymentDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (paymentDate.isAtSameMomentAs(yesterday)) {
        category = 'الأمس';
      } else if (paymentDate
              .isAfter(thisWeek.subtract(const Duration(days: 1))) &&
          paymentDate.isBefore(today)) {
        category = 'الأسبوع';
      } else if (paymentDate
              .isAfter(thisMonth.subtract(const Duration(days: 1))) &&
          paymentDate.isBefore(today)) {
        category = 'الشهر';
      } else {
        // عرض التسديدات القديمة تحت فئة "سابقاً"
        category = 'سابقاً';
      }

      organized.putIfAbsent(category, () => []);
      organized[category]!.add(payment);
      debugPrint('تم إضافة تسديد إلى فئة: $category');
    }

    // ترتيب الفئات حسب الأولوية
    final orderedCategories = [
      'اليوم',
      'الأمس',
      'الأسبوع',
      'الشهر',
      'سابقاً',
    ];

    final Map<String, List> orderedResult = {};
    for (final category in orderedCategories) {
      if (organized.containsKey(category)) {
        orderedResult[category] = organized[category]!;
        debugPrint(
            'فئة مرتبة: $category مع ${organized[category]!.length} تسديد');
      }
    }

    debugPrint('النتيجة النهائية: ${orderedResult.keys.toList()}');
    return orderedResult;
  }

  // دالة الحصول على ألوان الفئة الزمنية للتسديدات
  List<Color> _getPaymentCategoryColors(String category) {
    switch (category) {
      case 'اليوم':
        return [Colors.green.shade500, Colors.green.shade700];
      case 'الأمس':
        return [const Color(0xFF1A237E), const Color(0xFF0D1B69)];
      case 'الأسبوع':
        return [Colors.orange.shade500, Colors.orange.shade700];
      case 'الشهر':
        return [Colors.purple.shade500, Colors.purple.shade700];
      case 'سابقاً':
        return [Colors.brown.shade500, Colors.brown.shade700];
      default:
        return [Colors.grey.shade500, Colors.grey.shade700];
    }
  }

  // دالة الحصول على أيقونة الفئة الزمنية للتسديدات
  IconData _getPaymentCategoryIcon(String category) {
    switch (category) {
      case 'اليوم':
        return Icons.today;
      case 'الأمس':
        return Icons.schedule;
      case 'الأسبوع':
        return Icons.date_range;
      case 'الشهر':
        return Icons.calendar_month;
      case 'سابقاً':
        return Icons.history;
      default:
        return Icons.payment;
    }
  }

  // دالة حساب المبلغ الإجمالي
  double _getTotalAmount(List payments) {
    return payments.fold<double>(0, (sum, payment) => sum + payment.amount);
  }

  // دالة حساب الكمية الإجمالية
  int _getTotalQuantity(List payments) {
    int totalQuantity = 0;
    for (final payment in payments) {
      // البحث عن الدين المرتبط بالتسديد للحصول على الكمية
      final debts = Provider.of<DebtProvider>(context, listen: false)
          .debts
          .where((d) => d.id == payment.debtId);
      if (debts.isNotEmpty) {
        totalQuantity += debts.first.quantity;
      }
    }
    return totalQuantity;
  }

  // دالة لتنسيق العملة بدون رمز العملة
  String _formatCurrencyWithoutSymbol(double amount) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(amount.round());
  }

  // دالة لبناء قائمة بطاقات التسديدات تحت كل فئة
  Widget _buildCategoryPaymentsList(
      List payments, debtProvider, String category) {
    debugPrint(
        '_buildCategoryPaymentsList: category = $category, payments count = ${payments.length}');
    if (payments.isEmpty) {
      debugPrint(
          '_buildCategoryPaymentsList: payments is empty, returning SizedBox.shrink()');
      return const SizedBox.shrink();
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding:
          const EdgeInsets.symmetric(horizontal: 4), // تقليل المسافة الأفقية
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // بطاقتين في كل صف
        crossAxisSpacing: 4.0, // مسافة أفقية أصغر جداً بين البطاقات
        mainAxisSpacing:
            16.0, // مسافة عمودية كبيرة جداً بين الصفوف لإضافة بطاقات
        childAspectRatio:
            0.4, // نسبة محسنة لجعل البطاقة أطول لاحتواء الأزرار بشكل صحيح
      ),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        final debts = debtProvider.debts.where((d) => d.id == payment.debtId);
        final debt = debts.isNotEmpty ? debts.first : null;

        return PaymentCard(
          payment: payment,
          debt: debt,
          customer: widget.customer,
          isMiniView: true,
          categoryColors: () {
            final colors = _getPaymentCategoryColors(category);
            debugPrint('PaymentsTab: category = $category, colors = $colors');
            return colors;
          }(),
          paymentCount: index + 1,
          totalPayments: payments.length,
        );
      },
    );
  }

  // دالة لبناء عرض التسديدات حسب النوع المحدد
  Widget _buildPaymentsView(List customerPayments, debtProvider) {
    debugPrint('بناء عرض التسديدات - النوع الحالي: ${_currentViewType.name}');
    debugPrint('عدد التسديدات: ${customerPayments.length}');

    if (customerPayments.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد تسديدات',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // عرض التسديدات مع بطاقات التصنيف الزمني
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: _buildTimeClassificationCards(customerPayments, debtProvider),
    );
  }
}
