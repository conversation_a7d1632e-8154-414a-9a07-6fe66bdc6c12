import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/customer_statistics.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../database/database_helper.dart';

class CustomerStatisticsProvider extends ChangeNotifier {
  CustomerStatisticsProvider(this._debtProvider) {
    // الاستماع لتغييرات DebtProvider لإعادة حساب الإحصائيات تلقائياً
    _debtProvider.addListener(_onDebtProviderChanged);
  }

  final DebtProvider _debtProvider;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Map<String, CustomerStatistics> _customerStatistics = {};
  bool _isLoading = false;
  List<Customer> _lastCustomers = [];

  Map<String, CustomerStatistics> get customerStatistics => _customerStatistics;
  bool get isLoading => _isLoading;

  // دالة للاستماع لتغييرات DebtProvider
  void _onDebtProviderChanged() {
    // إعادة حساب الإحصائيات إذا كان لدينا عملاء محفوظين
    if (_lastCustomers.isNotEmpty && !_isLoading) {
      debugPrint('DebtProvider changed, recalculating statistics...');
      calculateStatistics(_lastCustomers);
    }
  }

  @override
  void dispose() {
    _debtProvider.removeListener(_onDebtProviderChanged);
    super.dispose();
  }

  Future<void> calculateStatistics(List<Customer> customers) async {
    _isLoading = true;
    notifyListeners();

    try {
      // حفظ قائمة العملاء لإعادة الحساب عند التحديث
      _lastCustomers = customers;

      final Map<String, CustomerStatistics> statistics = {};

      for (final customer in customers) {
        statistics[customer.id!.toString()] =
            await _calculateCustomerStatistics(customer);
      }

      _customerStatistics = statistics;
    } catch (e) {
      debugPrint('Error calculating statistics: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<CustomerStatistics> _calculateCustomerStatistics(
    Customer customer,
  ) async {
    final customerId = customer.id!;

    // جلب جميع الديون والمدفوعات للعميل
    final customerDebts = _debtProvider.debts
        .where((debt) => debt.customerId == customerId)
        .toList();

    final customerPayments = _debtProvider.payments
        .where((payment) => payment.customerId == customerId)
        .toList();

    // حساب التواريخ
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekStart = today.subtract(Duration(days: now.weekday - 1));
    final monthStart = DateTime(now.year, now.month);

    // إجمالي الديون
    final totalDebtsAmount = customerDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.remainingAmount,
    );
    final totalDebtsCount = customerDebts.length;

    // إجمالي الكارتات
    final totalCardsCount = customerDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // مبيعات اليوم (المبيعات فقط - ديون العملاء لك)
    final todayDebts = customerDebts
        .where((debt) =>
            _isSameDay(debt.entryDate, today) &&
            debt.direction == DebtDirection.customerOwesMe)
        .toList();
    final todaySalesAmount = todayDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.amount,
    );
    final todaySalesCount = todayDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // مبيعات الأمس (المبيعات فقط - ديون العملاء لك)
    final yesterdayDebts = customerDebts
        .where((debt) =>
            _isSameDay(debt.entryDate, yesterday) &&
            debt.direction == DebtDirection.customerOwesMe)
        .toList();
    final yesterdaySalesAmount = yesterdayDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.amount,
    );
    final yesterdaySalesCount = yesterdayDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // مبيعات الأسبوع
    final weekDebts = customerDebts
        .where(
          (debt) => debt.entryDate.isAfter(
            weekStart.subtract(const Duration(days: 1)),
          ),
        )
        .toList();
    final weekSalesAmount = weekDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.amount,
    );
    final weekSalesCount = weekDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // مبيعات الشهر
    final monthDebts = customerDebts
        .where(
          (debt) => debt.entryDate.isAfter(
            monthStart.subtract(const Duration(days: 1)),
          ),
        )
        .toList();
    final monthSalesAmount = monthDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.amount,
    );
    final monthSalesCount = monthDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // الديون المتأخرة
    final overdueDebts = customerDebts
        .where(
          (debt) => debt.dueDate.isBefore(today) && debt.remainingAmount > 0,
        )
        .toList();
    final overdueDebtsAmount = overdueDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.remainingAmount,
    );
    final overdueDebtsCount = overdueDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // أنواع الكارتات المتأخرة
    final overdueCardTypes = await _calculateCardTypeQuantities(overdueDebts);

    // الكميات حسب النوع
    final cardTypeQuantities = await _calculateCardTypeQuantities(
      customerDebts,
    );

    // المستحقات اليوم
    final dueTodayDebts = customerDebts
        .where(
          (debt) => _isSameDay(debt.dueDate, today) && debt.remainingAmount > 0,
        )
        .toList();
    final dueTodayAmount = dueTodayDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.remainingAmount,
    );
    final dueTodayCount = dueTodayDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // المستحقات القريبة (خلال 3 أيام)
    final nearFuture = today.add(const Duration(days: 3));
    final dueNearDebts = customerDebts
        .where(
          (debt) =>
              debt.dueDate.isAfter(today) &&
              debt.dueDate.isBefore(nearFuture.add(const Duration(days: 1))) &&
              debt.remainingAmount > 0,
        )
        .toList();
    final dueNearAmount = dueNearDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.remainingAmount,
    );
    final dueNearCount = dueNearDebts.fold<int>(
      0,
      (sum, debt) => sum + debt.quantity,
    );

    // المدفوعات اليوم
    final todayPayments = customerPayments
        .where((payment) => _isSameDay(payment.paymentDate, today))
        .toList();
    final paidTodayAmount = todayPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );
    final paidTodayCount = todayPayments.length;

    // المدفوعات أمس
    final yesterdayPayments = customerPayments
        .where((payment) => _isSameDay(payment.paymentDate, yesterday))
        .toList();
    final paidYesterdayAmount = yesterdayPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );
    final paidYesterdayCount = yesterdayPayments.length;

    // المدفوعات هذا الأسبوع
    final weekPayments = customerPayments
        .where(
          (payment) => payment.paymentDate.isAfter(
            weekStart.subtract(const Duration(days: 1)),
          ),
        )
        .toList();
    final paidWeekAmount = weekPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );
    final paidWeekCount = weekPayments.length;

    // إجمالي عدد الكارتات المسددة
    int totalPaidCardsCount = 0;
    for (final payment in customerPayments) {
      // البحث عن الدين المرتبط بهذه المدفوعة للحصول على عدد الكارتات
      try {
        final relatedDebt = customerDebts.firstWhere(
          (debt) => debt.id == payment.debtId,
        );
        totalPaidCardsCount += relatedDebt.quantity;
      } catch (e) {
        // إذا لم يتم العثور على الدين، نضيف 1 كافتراضي
        totalPaidCardsCount += 1;
      }
    }

    // إجمالي المبلغ المسدد
    final totalPaidAmount = customerPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );

    // نشاط العميل
    final activity = _calculateCustomerActivity(
      customerDebts,
      customerPayments,
    );

    // سلوك السداد
    final paymentBehavior = _calculatePaymentBehavior(
      customerDebts,
      customerPayments,
    );

    // آخر عملية تسديد
    final lastPayment = customerPayments.isNotEmpty
        ? customerPayments.reduce(
            (a, b) => a.paymentDate.isAfter(b.paymentDate) ? a : b,
          )
        : null;

    final lastPaymentDate = lastPayment?.paymentDate;
    final lastPaymentTime =
        lastPaymentDate != null ? _formatTime(lastPaymentDate) : null;
    final lastPaymentDay =
        lastPaymentDate != null ? _formatDayName(lastPaymentDate) : null;
    final paymentCounter = customerPayments.length;

    return CustomerStatistics(
      customerId: customerId.toString(),
      customerName: customer.name,
      totalDebtsAmount: totalDebtsAmount,
      totalDebtsCount: totalDebtsCount,
      totalCardsCount: totalCardsCount,
      todaySalesAmount: todaySalesAmount,
      todaySalesCount: todaySalesCount,
      yesterdaySalesAmount: yesterdaySalesAmount,
      yesterdaySalesCount: yesterdaySalesCount,
      weekSalesAmount: weekSalesAmount,
      weekSalesCount: weekSalesCount,
      monthSalesAmount: monthSalesAmount,
      monthSalesCount: monthSalesCount,
      overdueDebtsAmount: overdueDebtsAmount,
      overdueDebtsCount: overdueDebtsCount,
      overdueCardTypes: overdueCardTypes,
      cardTypeQuantities: cardTypeQuantities,
      dueTodayAmount: dueTodayAmount,
      dueTodayCount: dueTodayCount,
      dueNearAmount: dueNearAmount,
      dueNearCount: dueNearCount,
      paidTodayAmount: paidTodayAmount,
      paidTodayCount: paidTodayCount,
      paidYesterdayAmount: paidYesterdayAmount,
      paidYesterdayCount: paidYesterdayCount,
      paidWeekAmount: paidWeekAmount,
      paidWeekCount: paidWeekCount,
      totalPaidCardsCount: totalPaidCardsCount,
      totalPaidAmount: totalPaidAmount,
      activity: activity,
      paymentBehavior: paymentBehavior,
      lastPaymentDate: lastPaymentDate,
      lastPaymentTime: lastPaymentTime,
      lastPaymentDay: lastPaymentDay,
      paymentCounter: paymentCounter,
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Future<Map<String, CardTypeQuantity>> _calculateCardTypeQuantities(
    List<Debt> debts,
  ) async {
    final Map<String, CardTypeQuantity> quantities = {};

    for (final debt in debts) {
      // تحويل اسم الكارت إلى العربية
      final arabicCardType = await _getArabicCardTypeName(debt.cardType);

      if (quantities.containsKey(arabicCardType)) {
        final existing = quantities[arabicCardType]!;
        quantities[arabicCardType] = CardTypeQuantity(
          cardType: arabicCardType,
          quantity: existing.quantity + debt.quantity,
          amount: existing.amount + debt.remainingAmount,
        );
      } else {
        quantities[arabicCardType] = CardTypeQuantity(
          cardType: arabicCardType,
          quantity: debt.quantity,
          amount: debt.remainingAmount,
        );
      }
    }

    return quantities;
  }

  // دالة مساعدة لتحويل أسماء الكارتات من الإنجليزية إلى العربية
  Future<String> _getArabicCardTypeName(String cardType) async {
    // إذا كان النص فارغ، ارجع "غير محدد"
    if (cardType.isEmpty) {
      return 'غير محدد';
    }

    // التحقق من الأنواع الافتراضية أولاً
    try {
      final enumCardType = CardType.values.firstWhere(
        (type) => type.name.toLowerCase() == cardType.toLowerCase(),
      );
      return enumCardType.displayName;
    } catch (e) {
      // لم يتم العثور على النوع في enum
    }

    // التحقق من الأنواع المخصصة (تبدأ بـ custom_)
    if (cardType.startsWith('custom_')) {
      try {
        // استخراج معرف الكارت المخصص
        final customIdStr = cardType.replaceFirst('custom_', '');
        final customId = int.tryParse(customIdStr);

        if (customId != null) {
          // البحث في قاعدة البيانات عن الكارت المخصص
          final customCardType = await _dbHelper.getCustomCardTypeById(
            customId,
          );
          if (customCardType != null) {
            return customCardType.displayName; // الاسم العربي الأصلي
          }
        }
      } catch (e) {
        debugPrint('Error getting custom card type: $e');
      }

      // إذا فشل في الحصول على الاسم، ارجع نص افتراضي
      final customPart = cardType.replaceFirst('custom_', '');
      return 'نوع مخصص $customPart';
    }

    // التحقق من بعض الحالات الخاصة الإضافية
    switch (cardType.toLowerCase()) {
      case 'mada':
        return 'مدى';
      case 'stc':
        return 'STC';
      case 'mobily':
        return 'موبايلي';
      case 'zain':
        return 'زين';
      case 'sawa':
        return 'سوا';
      case 'jawwy':
        return 'جوي';
      case 'qitaf':
        return 'قطاف';
      case 'recharge':
        return 'كارت شحن';
      case 'internet':
        return 'كارت انترنت';
      case 'data':
        return 'كارت بيانات';
      case 'calls':
        return 'كارت مكالمات';
      default:
        // إذا كان النص يحتوي على أحرف عربية، ارجعه كما هو
        if (_containsArabic(cardType)) {
          return cardType;
        }
        // وإلا، ارجع النص الأصلي مع تحسين بسيط
        return _formatCardTypeName(cardType);
    }
  }

  // دالة مساعدة لتحسين تنسيق اسم الكارت
  String _formatCardTypeName(String cardType) {
    // تحويل الأحرف الأولى إلى كبيرة
    if (cardType.isEmpty) return cardType;

    return cardType
        .split('_')
        .map(
          (word) => word.isEmpty
              ? word
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        )
        .join(' ');
  }

  // دالة مساعدة للتحقق من وجود أحرف عربية
  bool _containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  CustomerActivity _calculateCustomerActivity(
    List<Debt> debts,
    List<Payment> payments,
  ) {
    final now = DateTime.now();
    final last30Days = now.subtract(const Duration(days: 30));

    final recentDebts =
        debts.where((debt) => debt.entryDate.isAfter(last30Days)).length;
    final recentPayments = payments
        .where((payment) => payment.paymentDate.isAfter(last30Days))
        .length;

    final totalActivity = recentDebts + recentPayments;

    if (totalActivity >= 20) return CustomerActivity.veryActive;
    if (totalActivity >= 10) return CustomerActivity.active;
    if (totalActivity >= 5) return CustomerActivity.moderate;
    if (totalActivity >= 1) return CustomerActivity.low;
    return CustomerActivity.inactive;
  }

  PaymentBehavior _calculatePaymentBehavior(
    List<Debt> debts,
    List<Payment> payments,
  ) {
    if (payments.isEmpty) return PaymentBehavior.irregular;

    int onTimeCount = 0;
    int lateCount = 0;
    int earlyCount = 0;

    for (final payment in payments) {
      final relatedDebt = debts.firstWhere(
        (debt) => debt.id == payment.debtId,
        orElse: () => debts.first,
      );

      if (payment.paymentDate.isBefore(relatedDebt.dueDate)) {
        earlyCount++;
      } else if (_isSameDay(payment.paymentDate, relatedDebt.dueDate)) {
        onTimeCount++;
      } else {
        lateCount++;
      }
    }

    final total = payments.length;
    if (earlyCount / total > 0.6) return PaymentBehavior.earlyPayer;
    if (onTimeCount / total > 0.6) return PaymentBehavior.onTime;
    if (lateCount / total > 0.6) return PaymentBehavior.latePayer;
    return PaymentBehavior.irregular;
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    if (hour < 12) {
      return '${DateFormat('h:mm').format(dateTime)} صباحاً';
    } else {
      return '${DateFormat('h:mm').format(dateTime)} مساءً';
    }
  }

  String _formatDayName(DateTime dateTime) {
    final dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return dayNames[dateTime.weekday - 1];
  }
}
